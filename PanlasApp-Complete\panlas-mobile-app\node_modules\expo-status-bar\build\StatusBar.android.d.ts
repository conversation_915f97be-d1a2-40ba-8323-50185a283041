import { StatusBarAnimation, StatusBarProps, StatusBarStyle } from './types';
export declare function StatusBar(props: StatusBarProps): import("react").JSX.Element;
export declare function setStatusBarStyle(style: StatusBarStyle, animated?: boolean): void;
export declare function setStatusBarHidden(hidden: boolean, animation?: StatusBarAnimation): void;
export declare function setStatusBarBackgroundColor(backgroundColor: string, animated?: boolean): void;
export declare function setStatusBarNetworkActivityIndicatorVisible(visible: boolean): void;
export declare function setStatusBarTranslucent(translucent: boolean): void;
//# sourceMappingURL=StatusBar.android.d.ts.map