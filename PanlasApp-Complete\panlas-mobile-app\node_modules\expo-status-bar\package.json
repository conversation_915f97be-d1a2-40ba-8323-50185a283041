{"name": "expo-status-bar", "version": "2.2.3", "description": "Provides the same interface as the React Native StatusBar API, but with slightly different defaults to work great in Expo environments.", "main": "src/StatusBar", "types": "build/StatusBar.d.ts", "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "expo-status-bar"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-status-bar"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/status-bar/", "dependencies": {"react-native-edge-to-edge": "1.6.0", "react-native-is-edge-to-edge": "^1.1.6"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^15.0.7", "@testing-library/react-native": "^13.1.0", "expo-module-scripts": "^4.1.6"}, "peerDependencies": {"react": "*", "react-native": "*"}, "jest": {"projects": [{"preset": "jest-expo/ios"}, {"preset": "jest-expo/android"}]}, "gitHead": "84355076bc31a356aa3d23257f387f221885f53d"}